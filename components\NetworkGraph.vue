<template>
  <div class="relative bg-gray-50 border border-gray-300 rounded-lg h-[600px]">
    <svg ref="svgRef" class="w-full h-full"></svg>
  </div>
</template>

<script setup lang="ts">
import * as d3 from 'd3'
import type { Port, Node, Link } from '~/types/network'

interface Props {
  nodes: Node[]
  links: Link[]
  selectedNode: Node | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'node-selected': [node: Node]
  'node-deselected': []
}>()

const svgRef = ref<SVGElement | null>(null)
let simulation: any = null

// Watch for changes in nodes/links to re-render
watch([() => props.nodes, () => props.links], () => {
  renderGraph()
}, { deep: true })

// Watch for selected node changes to update highlighting
watch(() => props.selectedNode, () => {
  updateNodeHighlighting()
})

onMounted(() => {
  setTimeout(() => {
    renderGraph()
  }, 100)
})

// Helper function to get node ID from port ID
const getNodeIdFromPortId = (portId: string): string => {
  const allPorts = props.nodes.flatMap(node => node.Ports)
  const port = allPorts.find(p => p.id === portId)
  return port ? port.deviceId : portId
}

// Helper function to convert port-based links to node-based links for D3
const convertLinksForD3 = (links: Link[]): any[] => {
  const convertedLinks = links.map(link => ({
    ...link,
    source: typeof link.source === 'string' ? getNodeIdFromPortId(link.source) : getNodeIdFromPortId(link.source.id),
    target: typeof link.target === 'string' ? getNodeIdFromPortId(link.target) : getNodeIdFromPortId(link.target.id),
    originalSource: link.source,
    originalTarget: link.target
  }))

  // Group links by node pairs and add curve offset for multiple links
  const linkGroups = new Map<string, any[]>()

  convertedLinks.forEach(link => {
    const key = [link.source, link.target].sort().join('-')
    if (!linkGroups.has(key)) {
      linkGroups.set(key, [])
    }
    linkGroups.get(key)!.push(link)
  })

  // Add curve offset for multiple links between same nodes
  linkGroups.forEach(group => {
    if (group.length > 1) {
      group.forEach((link, index) => {
        link.curveOffset = (index - (group.length - 1) / 2) * 30
        link.isMultiple = true
      })
    } else {
      group[0].curveOffset = 0
      group[0].isMultiple = false
    }
  })

  return convertedLinks
}

const renderGraph = () => {
  console.log('NetworkGraph renderGraph called', {
    nodes: props.nodes,
    links: props.links,
    svgRef: svgRef.value
  })

  if (!svgRef.value || !props.nodes.length) {
    console.log('NetworkGraph early return', {
      svgRef: !!svgRef.value,
      nodesLength: props.nodes.length
    })
    return
  }

  // Clear previous graph
  const svg = d3.select(svgRef.value)
  svg.selectAll('*').remove()

  const width = svg.node()!.getBoundingClientRect().width
  const height = svg.node()!.getBoundingClientRect().height

  console.log('NetworkGraph dimensions', { width, height })

  // Create a group for the graph
  const g = svg.append('g')

  // Define node colors based on type
  const nodeColor = (d: Node) => {
    if (d.type === 'router') return '#ff6b6b'
    if (d.type === 'switch') return '#4ecdc4'
    if (d.type === 'isp') return '#ffd166'
    return '#aaa'
  }

  // Create simulation with proper node data
  const nodesCopy = props.nodes.map(d => ({ ...d }))
  const linksCopy = convertLinksForD3(props.links)

  // Create links with thickness based on bandwidth usage
  const link = g.append('g')
    .attr('class', 'links')
    .selectAll('path')
    .data(linksCopy)
    .enter().append('path')
    .attr('stroke', (d: any) => {
      if (d.maxBandwidth && d.currentBandwidth) {
        const usage = d.currentBandwidth / d.maxBandwidth
        if (usage > 0.8) return '#ff0000'
        if (usage > 0.5) return '#ff9900'
        return '#999'
      }
      return '#999'
    })
    .attr('stroke-opacity', 0.6)
    .attr('stroke-width', (d: any) => {
      if (d.maxBandwidth) {
        return Math.max(1, 1 + (d.maxBandwidth / 500))
      }
      return 2
    })
    .attr('fill', 'none')

  // Add bandwidth labels to links
  const linkLabels = g.append('g')
    .attr('class', 'link-labels')
    .selectAll('text')
    .data(linksCopy)
    .enter().append('text')
    .attr('font-size', '10px')
    .attr('text-anchor', 'middle')
    .attr('dy', -5)
    .attr('fill', (d: any) => {
      if (d.maxBandwidth && d.currentBandwidth) {
        const usage = d.currentBandwidth / d.maxBandwidth
        if (usage > 0.8) return '#ff0000'
        if (usage > 0.5) return '#ff9900'
        return '#666'
      }
      return '#666'
    })
    .text((d: any) => {
      if (d.maxBandwidth && d.currentBandwidth) {
        if (d.isMultiple) {
          // Show port information for multiple links
          const sourcePort = typeof d.originalSource === 'string' ? d.originalSource : d.originalSource.id
          const targetPort = typeof d.originalTarget === 'string' ? d.originalTarget : d.originalTarget.id
          return `${d.currentBandwidth}/${d.maxBandwidth} Mbps (${sourcePort.split('-').pop()} → ${targetPort.split('-').pop()})`
        }
        return `${d.currentBandwidth}/${d.maxBandwidth} Mbps`
      }
      return ''
    })

  // Add white background for better readability
  const linkLabelBg = g.append('g')
    .attr('class', 'link-label-bg')
    .selectAll('rect')
    .data(linksCopy)
    .enter().append('rect')
    .attr('fill', 'white')
    .attr('opacity', 0.7)
    .attr('rx', 3)
    .attr('ry', 3)

  // Create nodes
  const node = g.append('g')
    .attr('class', 'nodes')
    .selectAll('g')
    .data(nodesCopy)
    .enter().append('g')
    .attr('class', 'node')
    .on('click', (event: MouseEvent, d: Node) => {
      event.stopPropagation()
      emit('node-selected', d)
    })
    .call(d3.drag<any, any>()
      .on('start', dragStarted)
      .on('drag', dragged)
      .on('end', dragEnded))

  // Add shapes to nodes
  node.each(function(d: Node) {
    const element = d3.select(this)
    if (d.type === 'isp') {
      element.append('circle')
        .attr('r', 15)
        .attr('fill', nodeColor(d))
        .attr('stroke', '#fff')
        .attr('stroke-width', 2)

      element.append('text')
        .attr('text-anchor', 'middle')
        .attr('dy', '0.3em')
        .attr('font-size', '10px')
        .attr('font-weight', 'bold')
        .text('ISP')
    } else {
      element.append('circle')
        .attr('r', 15)
        .attr('fill', nodeColor(d))
        .attr('stroke', '#fff')
        .attr('stroke-width', 2)
    }
  })

  // Add labels to nodes
  node.append('text')
    .attr('dy', 30)
    .attr('text-anchor', 'middle')
    .text((d: Node) => d.name)
    .attr('font-size', '12px')

  // Add click handler to clear selection
  svg.on('click', () => {
    emit('node-deselected')
  })

  simulation = d3.forceSimulation(nodesCopy)
    .force('link', d3.forceLink(linksCopy).id((d: any) => d.id).distance(100))
    .force('charge', d3.forceManyBody().strength(-300))
    .force('center', d3.forceCenter(width / 2, height / 2))
    .on('tick', ticked)

  function ticked() {
    link.attr('d', (d: any) => {
      const dx = d.target.x - d.source.x
      const dy = d.target.y - d.source.y
      const dr = Math.sqrt(dx * dx + dy * dy)

      if (d.isMultiple && d.curveOffset !== 0) {
        // Calculate control point for curved path
        const midX = (d.source.x + d.target.x) / 2
        const midY = (d.source.y + d.target.y) / 2

        // Perpendicular offset for curve
        const offsetX = -dy / dr * d.curveOffset
        const offsetY = dx / dr * d.curveOffset

        const controlX = midX + offsetX
        const controlY = midY + offsetY

        return `M${d.source.x},${d.source.y} Q${controlX},${controlY} ${d.target.x},${d.target.y}`
      } else {
        // Straight line for single links
        return `M${d.source.x},${d.source.y} L${d.target.x},${d.target.y}`
      }
    })

    linkLabels
      .attr('x', (d: any) => {
        if (d.isMultiple && d.curveOffset !== 0) {
          const dx = d.target.x - d.source.x
          const dy = d.target.y - d.source.y
          const dr = Math.sqrt(dx * dx + dy * dy)
          const midX = (d.source.x + d.target.x) / 2
          const offsetX = -dy / dr * d.curveOffset * 0.5
          return midX + offsetX
        }
        return (d.source.x + d.target.x) / 2
      })
      .attr('y', (d: any) => {
        if (d.isMultiple && d.curveOffset !== 0) {
          const dx = d.target.x - d.source.x
          const dy = d.target.y - d.source.y
          const dr = Math.sqrt(dx * dx + dy * dy)
          const midY = (d.source.y + d.target.y) / 2
          const offsetY = dx / dr * d.curveOffset * 0.5
          return midY + offsetY
        }
        return (d.source.y + d.target.y) / 2
      })

    linkLabelBg
      .attr('x', (d: any) => {
        let labelText = `${d.currentBandwidth}/${d.maxBandwidth} Mbps`
        if (d.isMultiple) {
          const sourcePort = typeof d.originalSource === 'string' ? d.originalSource : d.originalSource.id
          const targetPort = typeof d.originalTarget === 'string' ? d.originalTarget : d.originalTarget.id
          labelText = `${d.currentBandwidth}/${d.maxBandwidth} Mbps (${sourcePort.split('-').pop()} → ${targetPort.split('-').pop()})`
        }
        const labelWidth = labelText.length * 5.5
        if (d.isMultiple && d.curveOffset !== 0) {
          const dx = d.target.x - d.source.x
          const dy = d.target.y - d.source.y
          const dr = Math.sqrt(dx * dx + dy * dy)
          const midX = (d.source.x + d.target.x) / 2
          const offsetX = -dy / dr * d.curveOffset * 0.5
          return midX + offsetX - labelWidth / 2
        }
        return (d.source.x + d.target.x) / 2 - labelWidth / 2
      })
      .attr('y', (d: any) => {
        if (d.isMultiple && d.curveOffset !== 0) {
          const dx = d.target.x - d.source.x
          const dy = d.target.y - d.source.y
          const dr = Math.sqrt(dx * dx + dy * dy)
          const midY = (d.source.y + d.target.y) / 2
          const offsetY = dx / dr * d.curveOffset * 0.5
          return midY + offsetY - 15
        }
        return (d.source.y + d.target.y) / 2 - 15
      })
      .attr('width', (d: any) => {
        let labelText = `${d.currentBandwidth}/${d.maxBandwidth} Mbps`
        if (d.isMultiple) {
          const sourcePort = typeof d.originalSource === 'string' ? d.originalSource : d.originalSource.id
          const targetPort = typeof d.originalTarget === 'string' ? d.originalTarget : d.originalTarget.id
          labelText = `${d.currentBandwidth}/${d.maxBandwidth} Mbps (${sourcePort.split('-').pop()} → ${targetPort.split('-').pop()})`
        }
        return labelText.length * 5.5
      })
      .attr('height', 16)

    node.attr('transform', (d: any) => `translate(${d.x},${d.y})`)
  }

  function dragStarted(event: any, d: any) {
    if (!event.active) simulation.alphaTarget(0.3).restart()
    d.fx = d.x
    d.fy = d.y
  }

  function dragged(event: any, d: any) {
    d.fx = event.x
    d.fy = event.y
  }

  function dragEnded(event: any, d: any) {
    if (!event.active) simulation.alphaTarget(0)
    d.fx = null
    d.fy = null
  }

  updateNodeHighlighting()
}

const updateNodeHighlighting = () => {
  if (!svgRef.value) return

  d3.select(svgRef.value)
    .selectAll('.node circle')
    .attr('stroke', (d: any) => {
      return props.selectedNode && d.id === props.selectedNode.id ? '#333' : '#fff'
    })
    .attr('stroke-width', (d: any) => {
      return props.selectedNode && d.id === props.selectedNode.id ? 3 : 2
    })
}
</script>
